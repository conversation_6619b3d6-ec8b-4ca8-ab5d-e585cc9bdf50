import { useState } from "react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import {
  Page,
  Layout,
  Text,
  Card,
  Button,
  BlockStack,
  InlineStack,
  Badge,
  Icon,
  Divider,
  Grid,
  Link,
  Box,
} from "@shopify/polaris";
import {
  CheckIcon,
  PlayIcon,
  PersonIcon,
  FilterIcon,
  ViewIcon,
  SettingsIcon,
  CollectionIcon,
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  return null;
};

export default function Index() {
  // State for tracking onboarding progress
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);

  const quickStartSteps = [
    {
      id: 1,
      title: "Use AI to generate your first customer segment",
      description: "Let AI analyze your customers and create smart segments",
      icon: PersonIcon,
    },
    {
      id: 2,
      title: "Filter customers manually using 20+ audience traits",
      description: "Create precise segments with advanced filtering options",
      icon: FilterIcon,
    },
    {
      id: 3,
      title: "Send your first WhatsApp or Email campaign",
      description: "Engage your segments with targeted messaging",
      icon: ViewIcon,
    },
  ];

  const quickLinks = [
    {
      title: "Start with AI Search",
      description: "Generate segments using AI",
      url: "/app/dashboard",
      icon: PersonIcon,
      variant: "primary" as const,
    },
    {
      title: "Filter Customers Manually",
      description: "Use advanced filters",
      url: "/app/filter-audience",
      icon: FilterIcon,
      variant: "secondary" as const,
    },
    {
      title: "View Saved Lists",
      description: "Manage your segments",
      url: "/app/saved-lists",
      icon: CollectionIcon,
      variant: "secondary" as const,
    },
    {
      title: "Open Settings",
      description: "Configure your app",
      url: "/app/settings",
      icon: SettingsIcon,
      variant: "secondary" as const,
    },
  ];

  const toggleStep = (stepId: number) => {
    setCompletedSteps(prev =>
      prev.includes(stepId)
        ? prev.filter(id => id !== stepId)
        : [...prev, stepId]
    );
  };

  return (
    <Page>
      <TitleBar title="AI Audience Insight" />
      <BlockStack gap="500">
        <Layout>
          {/* Welcome Header Section */}
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <Text as="h1" variant="headingLg">
                    Welcome to Audience Insight
                  </Text>
                  <Text as="h2" variant="headingMd" tone="subdued">
                    Your smart assistant for understanding customers and sending powerful campaigns.
                  </Text>
                  <Text variant="bodyMd" as="p">
                    Use AI or manual filters to create high-converting segments and send WhatsApp or email messages — all in one place.
                  </Text>
                </BlockStack>
              </BlockStack>
            </Card>
          </Layout.Section>

          {/* Video Tutorial Section */}
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack gap="200" align="start">
                    <Icon source={PlayIcon} tone="base" />
                    <Text as="h3" variant="headingMd">
                      Watch: Complete Setup & First Campaign Tutorial
                    </Text>
                  </InlineStack>
                  <Text variant="bodyMd" as="p" tone="subdued">
                    Learn how to set up your first AI-powered customer segment and send targeted campaigns in under 10 minutes. This step-by-step guide covers everything from connecting your store to launching your first WhatsApp or email campaign.
                  </Text>
                </BlockStack>
                <Box
                  padding="400"
                  background="bg-surface-secondary"
                  borderRadius="200"
                  borderWidth="025"
                  borderColor="border"
                >
                  <div style={{
                    position: 'relative',
                    paddingBottom: '56.25%',
                    height: 0,
                    overflow: 'hidden',
                    borderRadius: '8px'
                  }}>
                    <iframe
                      src="https://www.youtube.com/embed/xNUx-rMGvvw"
                      title="Complete Setup & First Campaign Tutorial"
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        border: 'none'
                      }}
                      allowFullScreen
                    />
                  </div>
                </Box>
              </BlockStack>
            </Card>
          </Layout.Section>

          {/* Quick Start Checklist */}
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <InlineStack gap="200" align="space-between">
                    <Text as="h3" variant="headingMd">
                      Quick Start Guide
                    </Text>
                    <Badge tone="info">
                      {`${completedSteps.length}/${quickStartSteps.length} Completed`}
                    </Badge>
                  </InlineStack>
                  <Text variant="bodyMd" as="p" tone="subdued">
                    Follow these steps to get the most out of your app
                  </Text>
                </BlockStack>
                <BlockStack gap="300">
                  {quickStartSteps.map((step) => (
                    <Box
                      key={step.id}
                      padding="400"
                      background={completedSteps.includes(step.id) ? "bg-surface-success" : "bg-surface"}
                      borderRadius="200"
                      borderWidth="025"
                      borderColor={completedSteps.includes(step.id) ? "border-success" : "border"}
                    >
                      <InlineStack gap="300" align="space-between">
                        <InlineStack gap="300" align="start">
                          <Icon
                            source={completedSteps.includes(step.id) ? CheckIcon : step.icon}
                            tone={completedSteps.includes(step.id) ? "success" : "base"}
                          />
                          <BlockStack gap="100">
                            <Text as="p" variant="bodyMd" fontWeight="semibold">
                              {step.title}
                            </Text>
                            <Text as="p" variant="bodySm" tone="subdued">
                              {step.description}
                            </Text>
                          </BlockStack>
                        </InlineStack>
                        <Button
                          variant="plain"
                          onClick={() => toggleStep(step.id)}
                          accessibilityLabel={`Mark step ${step.id} as ${completedSteps.includes(step.id) ? 'incomplete' : 'complete'}`}
                        >
                          {completedSteps.includes(step.id) ? "Undo" : "Mark Complete"}
                        </Button>
                      </InlineStack>
                    </Box>
                  ))}
                </BlockStack>
              </BlockStack>
            </Card>
          </Layout.Section>

          {/* Optional: Key Metrics Card */}
          <Layout.Section variant="oneThird">
            <BlockStack gap="500">
              <Card>
                <BlockStack gap="400">
                  <Text as="h3" variant="headingMd">
                    Your Progress
                  </Text>
                  <BlockStack gap="300">
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Segments Created
                      </Text>
                      <Badge tone="info">0</Badge>
                    </InlineStack>
                    <Divider />
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Customers Messaged
                      </Text>
                      <Badge tone="info">0</Badge>
                    </InlineStack>
                    <Divider />
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Average Open Rate
                      </Text>
                      <Badge tone="info">--</Badge>
                    </InlineStack>
                    <Divider />
                    <InlineStack align="space-between">
                      <Text as="span" variant="bodyMd">
                        Repeat Purchases
                      </Text>
                      <Badge tone="info">0</Badge>
                    </InlineStack>
                  </BlockStack>
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="300">
                  <Text as="h3" variant="headingMd">
                    Need Help?
                  </Text>
                  <BlockStack gap="200">
                    <Button variant="plain" url="https://docs.audienceinsight.com" external>
                      📚 View Documentation
                    </Button>
                    <Button variant="plain" url="mailto:<EMAIL>" external>
                      🎧 Customer Care Team
                    </Button>
                    <Button 
                      variant="plain" 
                      onClick={() => {
                        // You can integrate with your chatbot service here
                        // For example: window.Intercom('show') or window.$crisp.push(['do', 'chat:open'])
                        alert('Opening live chat... (integrate with your chat service like Intercom, Crisp, or Zendesk)');
                      }}
                    >
                      🤖 Live Chat Support
                    </Button>
                    <Button variant="plain" url="https://community.audienceinsight.com" external>
                      👥 Join Community
                    </Button>
                  </BlockStack>
                </BlockStack>
              </Card>
            </BlockStack>
          </Layout.Section>

          {/* Quick Links Section */}
          <Layout.Section>
            <Card>
              <BlockStack gap="400">
                <BlockStack gap="200">
                  <Text as="h3" variant="headingMd">
                    Quick Actions
                  </Text>
                  <Text variant="bodyMd" as="p" tone="subdued">
                    Jump straight into the features you need
                  </Text>
                </BlockStack>
                <Grid>
                  {quickLinks.map((link, index) => (
                    <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
                      <Card>
                        <BlockStack gap="300">
                          <InlineStack gap="200" align="start">
                            <Icon source={link.icon} tone="base" />
                            <BlockStack gap="100">
                              <Text as="p" variant="bodyMd" fontWeight="semibold">
                                {link.title}
                              </Text>
                              <Text as="p" variant="bodySm" tone="subdued">
                                {link.description}
                              </Text>
                            </BlockStack>
                          </InlineStack>
                          <Button
                            variant={link.variant}
                            url={link.url}
                            fullWidth
                          >
                            {link.title}
                          </Button>
                        </BlockStack>
                      </Card>
                    </Grid.Cell>
                  ))}
                </Grid>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      </BlockStack>
    </Page>
  );
}
