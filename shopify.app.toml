# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "c67a67c0c68f98aa56393975ad2d958d"
name = "AI Audience Insights"
handle = "ai-audience-insights"
application_url = "https://soc-carry-quality-replacement.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products"

[auth]
redirect_urls = [
  "https://soc-carry-quality-replacement.trycloudflare.com/auth/callback",
  "https://soc-carry-quality-replacement.trycloudflare.com/auth/shopify/callback",
  "https://soc-carry-quality-replacement.trycloudflare.com/api/auth/callback"
]

[pos]
embedded = false
