/**
 * Settings Page for AI Audience Insight Shopify App
 *
 * This page provides a comprehensive settings interface for merchants to configure:
 * - WhatsApp and Email communication settings
 * - Plan selection and billing preferences
 * - Automated reporting schedules
 * - AI campaign suggestion preferences
 */

import { useState, useCallback } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useSubmit, useNavigation } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  FormLayout,
  TextField,
  Select,
  Button,
  Modal,
  Text,
  BlockStack,
  InlineStack,
  Checkbox,
  ChoiceList,
  Divider,
  Banner,
  Toast,
  Frame,
  Badge,
  Icon,
  Box,
} from "@shopify/polaris";
import {
  SettingsIcon,
  PhoneIcon,
  EmailIcon,
  CalendarIcon,
  MagicIcon,
  CheckIcon,
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

// ==========================================
// Server-side Functions
// ==========================================

/**
 * Loader Function - Fetch Current Settings
 *
 * Retrieves the merchant's current settings from the database.
 * In a real app, this would query your settings table.
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  // Mock current settings - replace with actual database query
  return {
    settings: {
      whatsappNumber: "+1234567890", // Current WhatsApp number
      emailId: "<EMAIL>", // Current email
      selectedPlan: "basic", // Current plan
      reportSchedule: {
        frequency: "weekly",
        day: "monday",
        time: "09:00"
      },
      aiSuggestions: true, // AI suggestions enabled/disabled
    }
  };
};

/**
 * Action Function - Save Settings
 *
 * Handles form submissions and saves settings to the database.
 * Also handles test message/email sending.
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  await authenticate.admin(request);

  const formData = await request.formData();
  const actionType = formData.get("actionType");

  try {
    switch (actionType) {
      case "saveSettings":
        // Save all settings to database
        const settings = {
          whatsappNumber: formData.get("whatsappNumber"),
          emailId: formData.get("emailId"),
          selectedPlan: formData.get("selectedPlan"),
          reportFrequency: formData.get("reportFrequency"),
          reportDay: formData.get("reportDay"),
          reportTime: formData.get("reportTime"),
          aiSuggestions: formData.get("aiSuggestions") === "true",
        };

        // In a real app, save to database here
        console.log("Saving settings:", settings);

        return {
          success: true,
          message: "Settings saved successfully!",
          settings
        };

      case "testWhatsApp":
        const whatsappNumber = formData.get("whatsappNumber");
        // In a real app, integrate with WhatsApp Business API
        console.log("Sending test WhatsApp to:", whatsappNumber);

        return {
          success: true,
          message: `Test WhatsApp message sent to ${whatsappNumber}`,
          type: "whatsapp"
        };

      case "testEmail":
        const emailId = formData.get("emailId");
        // In a real app, integrate with email service (SendGrid, etc.)
        console.log("Sending test email to:", emailId);

        return {
          success: true,
          message: `Test email sent to ${emailId}`,
          type: "email"
        };

      default:
        return {
          success: false,
          message: "Invalid action type"
        };
    }
  } catch (error) {
    return {
      success: false,
      message: "An error occurred while saving settings"
    };
  }
};
