/**
 * Settings Page for AI Audience Insight Shopify App
 *
 * This page provides a comprehensive settings interface for merchants to configure:
 * - WhatsApp and Email communication settings
 * - Plan selection and billing preferences
 * - Automated reporting schedules
 * - AI campaign suggestion preferences
 */

import { useState, useCallback } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, useSubmit, useNavigation } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  FormLayout,
  TextField,
  Select,
  Button,
  Modal,
  Text,
  BlockStack,
  InlineStack,
  Checkbox,
  ChoiceList,
  Divider,
  Banner,
  Toast,
  Frame,
  Badge,
  Icon,
  Box,
} from "@shopify/polaris";
import {
  SettingsIcon,
  PhoneIcon,
  EmailIcon,
  CalendarIcon,
  MagicIcon,
  CheckIcon,
} from "@shopify/polaris-icons";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

// ==========================================
// Server-side Functions
// ==========================================

/**
 * Loader Function - Fetch Current Settings
 *
 * Retrieves the merchant's current settings from the database.
 * In a real app, this would query your settings table.
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  // Mock current settings - replace with actual database query
  return {
    settings: {
      whatsappNumber: "+1234567890", // Current WhatsApp number
      emailId: "<EMAIL>", // Current email
      selectedPlan: "basic", // Current plan
      reportSchedule: {
        frequency: "weekly",
        day: "monday",
        time: "09:00"
      },
      aiSuggestions: true, // AI suggestions enabled/disabled
    }
  };
};

/**
 * Action Function - Save Settings
 *
 * Handles form submissions and saves settings to the database.
 * Also handles test message/email sending.
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  await authenticate.admin(request);

  const formData = await request.formData();
  const actionType = formData.get("actionType");

  try {
    switch (actionType) {
      case "saveSettings":
        // Save all settings to database
        const settings = {
          whatsappNumber: formData.get("whatsappNumber"),
          emailId: formData.get("emailId"),
          selectedPlan: formData.get("selectedPlan"),
          reportFrequency: formData.get("reportFrequency"),
          reportDay: formData.get("reportDay"),
          reportTime: formData.get("reportTime"),
          aiSuggestions: formData.get("aiSuggestions") === "true",
        };

        // In a real app, save to database here
        console.log("Saving settings:", settings);

        return {
          success: true,
          message: "Settings saved successfully!",
          settings
        };

      case "testWhatsApp":
        const whatsappNumber = formData.get("whatsappNumber");
        // In a real app, integrate with WhatsApp Business API
        console.log("Sending test WhatsApp to:", whatsappNumber);

        return {
          success: true,
          message: `Test WhatsApp message sent to ${whatsappNumber}`,
          type: "whatsapp"
        };

      case "testEmail":
        const emailId = formData.get("emailId");
        // In a real app, integrate with email service (SendGrid, etc.)
        console.log("Sending test email to:", emailId);

        return {
          success: true,
          message: `Test email sent to ${emailId}`,
          type: "email"
        };

      default:
        return {
          success: false,
          message: "Invalid action type"
        };
    }
  } catch (error) {
    return {
      success: false,
      message: "An error occurred while saving settings"
    };
  }
};

// ==========================================
// Types and Interfaces
// ==========================================

interface Settings {
  whatsappNumber: string;
  emailId: string;
  selectedPlan: string;
  reportSchedule: {
    frequency: string;
    day: string;
    time: string;
  };
  aiSuggestions: boolean;
}

interface LoaderData {
  settings: Settings;
}

interface ActionData {
  success: boolean;
  message: string;
  type?: string;
  settings?: Settings;
}

// ==========================================
// Main Settings Component
// ==========================================

/**
 * Main Settings Page Component
 *
 * Provides a comprehensive settings interface with modals for
 * WhatsApp/Email configuration and form controls for all other settings.
 */
export default function SettingsPage() {
  // ==========================================
  // Hooks and State Management
  // ==========================================

  const { settings } = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();
  const submit = useSubmit();
  const navigation = useNavigation();

  // Modal states
  const [whatsappModalOpen, setWhatsappModalOpen] = useState(false);
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [scheduleModalOpen, setScheduleModalOpen] = useState(false);

  // Form states
  const [whatsappNumber, setWhatsappNumber] = useState(settings.whatsappNumber);
  const [emailId, setEmailId] = useState(settings.emailId);
  const [selectedPlan, setSelectedPlan] = useState(settings.selectedPlan);
  const [reportFrequency, setReportFrequency] = useState(settings.reportSchedule.frequency);
  const [reportDay, setReportDay] = useState(settings.reportSchedule.day);
  const [reportTime, setReportTime] = useState(settings.reportSchedule.time);
  const [aiSuggestions, setAiSuggestions] = useState(settings.aiSuggestions);

  // Toast state for success/error messages
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastError, setToastError] = useState(false);

  // Loading states
  const isLoading = navigation.state === "submitting";
  const isSaving = navigation.formData?.get("actionType") === "saveSettings";

  // ==========================================
  // Event Handlers
  // ==========================================

  // Handle form submission for saving all settings
  const handleSaveSettings = useCallback(() => {
    const formData = new FormData();
    formData.append("actionType", "saveSettings");
    formData.append("whatsappNumber", whatsappNumber);
    formData.append("emailId", emailId);
    formData.append("selectedPlan", selectedPlan);
    formData.append("reportFrequency", reportFrequency);
    formData.append("reportDay", reportDay);
    formData.append("reportTime", reportTime);
    formData.append("aiSuggestions", aiSuggestions.toString());

    submit(formData, { method: "post" });
  }, [submit, whatsappNumber, emailId, selectedPlan, reportFrequency, reportDay, reportTime, aiSuggestions]);

  // Handle WhatsApp test message
  const handleTestWhatsApp = useCallback(() => {
    const formData = new FormData();
    formData.append("actionType", "testWhatsApp");
    formData.append("whatsappNumber", whatsappNumber);

    submit(formData, { method: "post" });
  }, [submit, whatsappNumber]);

  // Handle email test
  const handleTestEmail = useCallback(() => {
    const formData = new FormData();
    formData.append("actionType", "testEmail");
    formData.append("emailId", emailId);

    submit(formData, { method: "post" });
  }, [submit, emailId]);

  // Show toast messages based on action results
  const showToast = useCallback((message: string, isError = false) => {
    setToastMessage(message);
    setToastError(isError);
    setToastActive(true);
  }, []);

  // Handle action data changes (success/error responses)
  useState(() => {
    if (actionData) {
      showToast(actionData.message, !actionData.success);

      // Close modals on successful test
      if (actionData.success && actionData.type === "whatsapp") {
        setWhatsappModalOpen(false);
      }
      if (actionData.success && actionData.type === "email") {
        setEmailModalOpen(false);
      }
    }
  });

  // ==========================================
  // Plan Options Configuration
  // ==========================================

  const planOptions = [
    { label: "Free Plan - Basic features", value: "free" },
    { label: "Basic Plan - $29/month", value: "basic" },
    { label: "Growth Plan - $79/month", value: "growth" },
    { label: "Enterprise Plan - $199/month", value: "enterprise" },
  ];

  const frequencyOptions = [
    { label: "Daily", value: "daily" },
    { label: "Weekly", value: "weekly" },
    { label: "Monthly", value: "monthly" },
  ];

  const dayOptions = [
    { label: "Monday", value: "monday" },
    { label: "Tuesday", value: "tuesday" },
    { label: "Wednesday", value: "wednesday" },
    { label: "Thursday", value: "thursday" },
    { label: "Friday", value: "friday" },
    { label: "Saturday", value: "saturday" },
    { label: "Sunday", value: "sunday" },
  ];

  // ==========================================
  // Component Render
  // ==========================================

  return (
    <Frame>
      <Page fullWidth>
        <TitleBar title="Settings" />

        {/* Toast for success/error messages */}
        {toastActive && (
          <Toast
            content={toastMessage}
            error={toastError}
            onDismiss={() => setToastActive(false)}
          />
        )}

        <Layout>
          <Layout.Section>
            <BlockStack gap="800">

              {/* Page Header */}
              <Card>
                <BlockStack gap="400">
                  <InlineStack align="space-between">
                    <Text as="h1" variant="headingXl">
                      App Settings
                    </Text>
                    <Badge tone="info">
                      Configuration
                    </Badge>
                  </InlineStack>
                  <Text as="p" variant="bodyLg" tone="subdued">
                    Configure your AI Audience Insight app preferences, communication settings, and automation options.
                  </Text>
                  <Divider />
                </BlockStack>
              </Card>

              {/* Main Settings Form */}
              <Card>
                <FormLayout>

                  {/* ==========================================
                       Communication Settings Section
                       ========================================== */}

                  <BlockStack gap="500">
                    <Text as="h2" variant="headingLg">
                      Communication Settings
                    </Text>

                    {/* WhatsApp Number Setting */}
                    <FormLayout.Group>
                      <InlineStack align="space-between" blockAlign="center">
                        <BlockStack gap="200">
                          <InlineStack gap="200" blockAlign="center">
                            <Icon source={PhoneIcon} tone="base" />
                            <Text as="h3" variant="headingMd">
                              WhatsApp Number
                            </Text>
                          </InlineStack>
                          <Text as="p" variant="bodySm" tone="subdued">
                            Current: {whatsappNumber || "Not set"}
                          </Text>
                        </BlockStack>
                        <Button
                          onClick={() => setWhatsappModalOpen(true)}
                          variant="secondary"
                        >
                          Set Number
                        </Button>
                      </InlineStack>
                    </FormLayout.Group>

                    {/* Email ID Setting */}
                    <FormLayout.Group>
                      <InlineStack align="space-between" blockAlign="center">
                        <BlockStack gap="200">
                          <InlineStack gap="200" blockAlign="center">
                            <Icon source={EmailIcon} tone="base" />
                            <Text as="h3" variant="headingMd">
                              Email Address
                            </Text>
                          </InlineStack>
                          <Text as="p" variant="bodySm" tone="subdued">
                            Current: {emailId || "Not set"}
                          </Text>
                        </BlockStack>
                        <Button
                          onClick={() => setEmailModalOpen(true)}
                          variant="secondary"
                        >
                          Set Email
                        </Button>
                      </InlineStack>
                    </FormLayout.Group>
                  </BlockStack>

                  <Divider />

                  {/* ==========================================
                       Plan Selection Section
                       ========================================== */}

                  <BlockStack gap="400">
                    <Text as="h2" variant="headingLg">
                      Plan & Billing
                    </Text>

                    <Select
                      label="Select Your Plan"
                      options={planOptions}
                      value={selectedPlan}
                      onChange={setSelectedPlan}
                      helpText="Choose the plan that best fits your business needs"
                    />

                    {selectedPlan !== "free" && (
                      <Banner tone="info">
                        <p>
                          You're currently on the <strong>{selectedPlan}</strong> plan.
                          Changes will be reflected in your next billing cycle.
                        </p>
                      </Banner>
                    )}
                  </BlockStack>

                  <Divider />

                  {/* ==========================================
                       Reporting Schedule Section
                       ========================================== */}

                  <BlockStack gap="400">
                    <InlineStack align="space-between" blockAlign="center">
                      <BlockStack gap="200">
                        <InlineStack gap="200" blockAlign="center">
                          <Icon source={CalendarIcon} tone="base" />
                          <Text as="h2" variant="headingLg">
                            Automated Reports
                          </Text>
                        </InlineStack>
                        <Text as="p" variant="bodySm" tone="subdued">
                          Current: {reportFrequency} on {reportDay}s at {reportTime}
                        </Text>
                      </BlockStack>
                      <Button
                        onClick={() => setScheduleModalOpen(true)}
                        variant="secondary"
                      >
                        Set Schedule
                      </Button>
                    </InlineStack>
                  </BlockStack>

                  <Divider />

                  {/* ==========================================
                       AI Suggestions Section
                       ========================================== */}

                  <BlockStack gap="400">
                    <InlineStack gap="200" blockAlign="center">
                      <Icon source={MagicIcon} tone="base" />
                      <Text as="h2" variant="headingLg">
                        AI Campaign Suggestions
                      </Text>
                    </InlineStack>

                    <Checkbox
                      label="Enable AI-powered campaign suggestions"
                      checked={aiSuggestions}
                      onChange={setAiSuggestions}
                      helpText="Get intelligent recommendations for customer segments and marketing campaigns"
                    />

                    {aiSuggestions && (
                      <Banner tone="success">
                        <p>
                          <Icon source={CheckIcon} /> AI suggestions are enabled.
                          You'll receive smart recommendations based on your customer data.
                        </p>
                      </Banner>
                    )}
                  </BlockStack>

                  <Divider />

                  {/* ==========================================
                       Save Settings Button
                       ========================================== */}

                  <InlineStack align="end">
                    <Button
                      variant="primary"
                      onClick={handleSaveSettings}
                      loading={isSaving}
                      size="large"
                    >
                      Save All Settings
                    </Button>
                  </InlineStack>

                </FormLayout>
              </Card>
            </BlockStack>
          </Layout.Section>
        </Layout>

        {/* ==========================================
             WhatsApp Modal
             ========================================== */}

        <Modal
          open={whatsappModalOpen}
          onClose={() => setWhatsappModalOpen(false)}
          title="Set WhatsApp Number"
          primaryAction={{
            content: "Save Number",
            onAction: () => {
              setWhatsappModalOpen(false);
              showToast("WhatsApp number updated successfully!");
            },
          }}
          secondaryActions={[
            {
              content: "Send Test Message",
              onAction: handleTestWhatsApp,
              disabled: !whatsappNumber,
              loading: navigation.formData?.get("actionType") === "testWhatsApp",
            },
          ]}
        >
          <Modal.Section>
            <BlockStack gap="400">
              <Text as="p" variant="bodyMd">
                Enter your WhatsApp Business number to receive notifications and send customer messages.
              </Text>

              <TextField
                label="WhatsApp Number"
                value={whatsappNumber}
                onChange={setWhatsappNumber}
                placeholder="+1234567890"
                helpText="Include country code (e.g., +1 for US)"
                autoComplete="tel"
              />

              <Banner tone="info">
                <p>
                  Make sure this number is connected to WhatsApp Business API for automated messaging.
                </p>
              </Banner>
            </BlockStack>
          </Modal.Section>
        </Modal>

        {/* ==========================================
             Email Modal
             ========================================== */}

        <Modal
          open={emailModalOpen}
          onClose={() => setEmailModalOpen(false)}
          title="Set Email Address"
          primaryAction={{
            content: "Save Email",
            onAction: () => {
              setEmailModalOpen(false);
              showToast("Email address updated successfully!");
            },
          }}
          secondaryActions={[
            {
              content: "Send Test Email",
              onAction: handleTestEmail,
              disabled: !emailId,
              loading: navigation.formData?.get("actionType") === "testEmail",
            },
          ]}
        >
          <Modal.Section>
            <BlockStack gap="400">
              <Text as="p" variant="bodyMd">
                Enter your email address to receive reports, notifications, and important updates.
              </Text>

              <TextField
                label="Email Address"
                value={emailId}
                onChange={setEmailId}
                placeholder="<EMAIL>"
                type="email"
                autoComplete="email"
              />

              <Banner tone="info">
                <p>
                  This email will be used for automated reports and system notifications.
                </p>
              </Banner>
            </BlockStack>
          </Modal.Section>
        </Modal>

        {/* ==========================================
             Schedule Modal
             ========================================== */}

        <Modal
          open={scheduleModalOpen}
          onClose={() => setScheduleModalOpen(false)}
          title="Set Report Schedule"
          primaryAction={{
            content: "Save Schedule",
            onAction: () => {
              setScheduleModalOpen(false);
              showToast("Report schedule updated successfully!");
            },
          }}
        >
          <Modal.Section>
            <FormLayout>
              <Text as="p" variant="bodyMd">
                Configure when you want to receive automated customer insight reports.
              </Text>

              <Select
                label="Report Frequency"
                options={frequencyOptions}
                value={reportFrequency}
                onChange={setReportFrequency}
              />

              {reportFrequency === "weekly" && (
                <Select
                  label="Day of Week"
                  options={dayOptions}
                  value={reportDay}
                  onChange={setReportDay}
                />
              )}

              <TextField
                label="Time"
                value={reportTime}
                onChange={setReportTime}
                type="time"
                helpText="Time in 24-hour format"
                autoComplete="off"
              />

              <Banner tone="success">
                <p>
                  Reports will be automatically generated and sent to your email address.
                </p>
              </Banner>
            </FormLayout>
          </Modal.Section>
        </Modal>

      </Page>
    </Frame>
  );
}
