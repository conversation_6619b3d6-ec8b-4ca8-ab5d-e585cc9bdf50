import { useState } from "react";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  BlockStack,
  InlineStack,
  Text,
  Button,
  Divider,
  Checkbox,
  Grid,
  Banner,
  Tag,
  Collapsible,
  AppProvider,
  Badge
} from "@shopify/polaris";
import polarisTranslations from "@shopify/polaris/locales/en.json";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";
import { TitleBar } from "@shopify/app-bridge-react";
import { ChevronDownIcon, ChevronUpIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";

export const links = () => [{ rel: "stylesheet", href: polarisStyles }];

// Loader function to authenticate and provide initial data
export const loader = async ({ request }: LoaderFunctionArgs) => {
  await authenticate.admin(request);

  // In a real app, you would fetch data from your database or Shopify API
  return {
    // Mock data for demonstration
    locations: {
      popular: ["United States", "Canada", "United Kingdom", "Australia"],
      regions: ["North America", "Europe", "Asia", "South America", "Africa", "Oceania"],
      international: ["India", "Germany", "France", "Japan", "Brazil", "Mexico"]
    },
    products: ["Product A", "Product B", "Product C", "Product D"],
    categories: ["Category 1", "Category 2", "Category 3"],
    collections: ["Summer Collection", "Winter Collection", "Special Offers"],

    // Polaris translations
    polarisTranslations
  };
};

// Action function to handle form submissions
export const action = async ({ request }: ActionFunctionArgs) => {
  await authenticate.admin(request);

  // Get form data
  const formData = await request.formData();
  const filterData = Object.fromEntries(formData);

  // In a real app, this would create a Prisma query or Shopify GraphQL query
  // For now, we'll just return the filter data
  return new Response(JSON.stringify({
    success: true,
    filterData,
    matchCount: Math.floor(Math.random() * 100) // Mock count of matching customers
  }), {
    headers: {
      "Content-Type": "application/json",
    },
  });
};

// Main component for the route
export default function FilterAudiencePage() {
  // We'll use the loader data in the future when connecting to real data
  const data = useLoaderData<typeof loader>();

  return (
    <AppProvider i18n={data.polarisTranslations}>
      <Page fullWidth>
        <TitleBar title="Filter Audience" />
        <Layout>
          <Layout.Section>
            <BlockStack gap="800">
              <Card>
                <BlockStack gap="400">
                  <InlineStack align="space-between">
                    <Text as="h2" variant="headingXl">Filter Audience</Text>
                  </InlineStack>
                  <Text as="p" variant="bodyLg" tone="subdued">
                    Create targeted customer segments by applying multiple filters. Use these segments for marketing campaigns, personalized offers, or detailed analysis.
                  </Text>
                  <Divider />
                </BlockStack>
              </Card>
              <AudienceFilterForm />
            </BlockStack>
          </Layout.Section>
        </Layout>
      </Page>
    </AppProvider>
  );
}

// Component for the audience filter form
function AudienceFilterForm() {
  // State for form sections
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    location: true,
    products: true,
    timing: true,
    device: true,
    traffic: true,
    account: true,
    visits: true,
    cart: true,
    payment: true,
    delivery: true,
    returns: true,
    reviews: true,
    frequency: true,
    wishlist: true,
    engagement: true,
    clv: true,
    recency: true,
    pages: true,
    support: true
  });

  // State for selected filters
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({
    location: [],
    products: [],
    timing: [],
    device: [],
    traffic: [],
    account: [],
    payment: [],
    delivery: []
  });

  // State for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [results, setResults] = useState<any>(null);

  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections({
      ...expandedSections,
      [section]: !expandedSections[section]
    });
  };

  // Handle checkbox changes
  const handleCheckboxChange = (section: string, value: string, checked: boolean) => {
    if (checked) {
      setSelectedFilters({
        ...selectedFilters,
        [section]: [...(selectedFilters[section] || []), value]
      });
    } else {
      setSelectedFilters({
        ...selectedFilters,
        [section]: (selectedFilters[section] || []).filter(item => item !== value)
      });
    }
  };

  // Handle form submission
  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setResults({
        matchCount: Math.floor(Math.random() * 1000) + 1,
        filters: selectedFilters
      });
      setIsSubmitting(false);
    }, 1500);
  };

  // Render a section with checkboxes
  const renderCheckboxSection = (title: string, section: string, options: string[]) => {
    return (
      <Card>
        <BlockStack gap="400">
          <InlineStack align="space-between">
            <Text as="h3" variant="headingMd">{title}</Text>
            <Button
              variant="plain"
              icon={expandedSections[section] ? ChevronUpIcon : ChevronDownIcon}
              onClick={() => toggleSection(section)}
            />
          </InlineStack>

          <Collapsible open={expandedSections[section]} id={`section-${section}`}>
            <Grid>
              {options.map((option, index) => (
                <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 4, md: 4, lg: 4, xl: 4 }}>
                  <Checkbox
                    label={option}
                    checked={selectedFilters[section]?.includes(option) || false}
                    onChange={(checked) => handleCheckboxChange(section, option, checked)}
                  />
                </Grid.Cell>
              ))}
            </Grid>
          </Collapsible>
        </BlockStack>
      </Card>
    );
  };

  return (
    <Form method="post" onSubmit={handleSubmit}>
      <BlockStack gap="800">
        {/* Location Section */}
        {renderCheckboxSection(
          "Location",
          "location",
          [
            "United States", "Canada", "United Kingdom", "Australia",
            "India", "Germany", "France", "Japan", "Brazil", "Mexico",
            "North America", "Europe", "Asia", "South America"
          ]
        )}

        {/* Products Ordered Section */}
        {renderCheckboxSection(
          "Products Ordered",
          "products",
          [
            "Product A", "Product B", "Product C", "Product D",
            "Category 1", "Category 2", "Category 3",
            "Summer Collection", "Winter Collection", "Special Offers"
          ]
        )}

        {/* Visit Timing Section */}
        {renderCheckboxSection(
          "Visit Timing",
          "timing",
          [
            "Morning (6am-12pm)", "Afternoon (12pm-6pm)", "Evening (6pm-12am)", "Night (12am-6am)",
            "Weekdays", "Weekends", "Holidays", "Sale Events"
          ]
        )}

        {/* Device Type Section */}
        {renderCheckboxSection(
          "Device Type",
          "device",
          [
            "Desktop", "Mobile", "Tablet", "iOS", "Android", "Windows", "Mac"
          ]
        )}

        {/* Payment & Delivery Section - 4 columns */}
        <Grid>
          <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 6, lg: 6, xl: 6 }}>
            {renderCheckboxSection(
              "Payment Preferences",
              "payment",
              [
                "Credit Card", "PayPal", "Apple Pay", "Google Pay",
                "Cash on Delivery", "Bank Transfer", "Gift Card", "Store Credit"
              ]
            )}
          </Grid.Cell>

          <Grid.Cell columnSpan={{ xs: 6, sm: 6, md: 6, lg: 6, xl: 6 }}>
            {renderCheckboxSection(
              "Delivery Options",
              "delivery",
              [
                "Standard Shipping", "Express Shipping", "Free Shipping", "Local Pickup",
                "Same-day Delivery", "International Shipping", "Scheduled Delivery", "Eco-friendly Packaging"
              ]
            )}
          </Grid.Cell>
        </Grid>

        {/* Action Buttons */}
        <InlineStack align="end" gap="200">
          <Button onClick={() => setSelectedFilters({})}>
            Clear All Filters
          </Button>
          <Button variant="primary" submit loading={isSubmitting}>
            Generate List
          </Button>
        </InlineStack>

        {/* Results Section */}
        {results && (
          <Card>
            <BlockStack gap="400">
              <InlineStack align="space-between">
                <Text as="h2" variant="headingLg">Results</Text>
                <Badge tone="success">{`${results.matchCount} customers match your filters`}</Badge>
              </InlineStack>

              <Banner tone="info">
                <p>Your audience segment has been created successfully. You can now use this segment for marketing campaigns or further analysis.</p>
              </Banner>

              <BlockStack gap="200">
                <Text as="h3" variant="headingMd">Applied Filters:</Text>
                <InlineStack gap="200" wrap>
                  {Object.entries(results.filters).flatMap(([section, values]) =>
                    (values as string[]).map((value, index) => (
                      <Tag key={`${section}-${index}`}>{value}</Tag>
                    ))
                  )}
                </InlineStack>
              </BlockStack>

              <InlineStack align="end" gap="200">
                <Button>Export List</Button>
                <Button variant="primary">Create Campaign</Button>
              </InlineStack>
            </BlockStack>
          </Card>
        )}
      </BlockStack>
    </Form>
  );
}
