/**
 * Customer Insights Dashboard
 *
 * This file implements a comprehensive dashboard that displays key customer metrics
 * using Shopify's Polaris design system and Chart.js for visualizations.
 *
 * The dashboard is divided into four main sections:
 * 1. Customers Overview
 * 2. Purchase & Order Behavior
 * 3. Engagement Patterns
 * 4. Purchase Timing
 *
 * Each section contains relevant KPI cards and visualizations.
 */

import { useState } from "react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Text,
  Card,
  BlockStack,
  InlineStack,
  Badge,
  Button,
  Grid,
  Modal,
  EmptyState,
  Spinner,
  Select,
  AppProvider,
  Layout
} from "@shopify/polaris";
import polarisTranslations from "@shopify/polaris/locales/en.json";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";
import { TitleBar } from "@shopify/app-bridge-react";
import { RefreshIcon, ClipboardIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import { Bar, Pie, Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

export const links = () => [{ rel: "stylesheet", href: polarisStyles }];

/**
 * Loader function that provides data for the dashboard
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  // Authenticate the request to ensure it's coming from a valid Shopify store
  await authenticate.admin(request);

  // MOCK DATA
  // In a production app, you would fetch this data from Shopify's Admin API

  // Customer Overview data
  const totalCustomers = 1000;
  const newCustomers = 120;
  const returningCustomers = 350;
  const inactiveCustomers = 200;

  // Purchase & Order Behavior data
  const codOrders = 400;
  const prepaidOrders = 500;
  const cancelledOrders = 100;
  const abandonedCarts = 50;

  // Engagement Patterns data
  const discountUsers = 50;
  const wishlistUsers = 35;
  const reviewers = 25;
  const emailSubscribers = 450;

  // Purchase Timing data
  const morningPurchases = 200;
  const afternoonPurchases = 300;
  const eveningPurchases = 400;
  const weekendPurchases = 350;

  // Chart data for Customer Segmentation
  const orderTypeData = {
    labels: ["COD Orders", "Prepaid Orders", "Cancelled Orders", "Abandoned Carts"],
    datasets: [
      {
        data: [codOrders, prepaidOrders, cancelledOrders, abandonedCarts],
        backgroundColor: [
          "rgba(255, 99, 132, 0.7)",
          "rgba(54, 162, 235, 0.7)",
          "rgba(255, 206, 86, 0.7)",
          "rgba(75, 192, 192, 0.7)",
        ],
        borderColor: [
          "rgba(255, 99, 132, 1)",
          "rgba(54, 162, 235, 1)",
          "rgba(255, 206, 86, 1)",
          "rgba(75, 192, 192, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };

  // Chart data for Behavioral Breakdown
  const engagementData = {
    labels: ["Discount Users", "Wishlist Users", "Reviewers", "Email Subscribers"],
    datasets: [
      {
        label: "Number of Users",
        data: [discountUsers, wishlistUsers, reviewers, emailSubscribers],
        backgroundColor: "rgba(54, 162, 235, 0.7)",
        borderColor: "rgba(54, 162, 235, 1)",
        borderWidth: 1,
      },
    ],
  };

  // Mini chart data for trend lines
  const generateMiniChartData = (value: number, trend: "up" | "down" | "stable" = "up") => {
    const baseData = [value * 0.7, value * 0.8, value * 0.75, value * 0.9];

    if (trend === "up") {
      return {
        labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
        datasets: [{
          data: [...baseData, value * 0.95, value],
          borderColor: "rgba(75, 192, 192, 1)",
          backgroundColor: "rgba(75, 192, 192, 0.2)",
          fill: true,
        }]
      };
    } else if (trend === "down") {
      return {
        labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
        datasets: [{
          data: [value * 1.3, value * 1.2, value * 1.1, value * 1.05, value * 1.02, value],
          borderColor: "rgba(255, 99, 132, 1)",
          backgroundColor: "rgba(255, 99, 132, 0.2)",
          fill: true,
        }]
      };
    } else {
      return {
        labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
        datasets: [{
          data: [value * 0.95, value * 1.05, value * 0.98, value * 1.02, value * 0.99, value],
          borderColor: "rgba(54, 162, 235, 1)",
          backgroundColor: "rgba(54, 162, 235, 0.2)",
          fill: true,
        }]
      };
    }
  };

  // Generate mini chart data for each metric
  const customerGrowthData = generateMiniChartData(totalCustomers, "up");
  const newCustomersData = generateMiniChartData(newCustomers, "up");
  const returningCustomersData = generateMiniChartData(returningCustomers, "up");
  const inactiveCustomersData = generateMiniChartData(inactiveCustomers, "down");

  const codOrdersData = generateMiniChartData(codOrders, "up");
  const prepaidOrdersData = generateMiniChartData(prepaidOrders, "up");
  const cancelledOrdersData = generateMiniChartData(cancelledOrders, "down");
  const abandonedCartsData = generateMiniChartData(abandonedCarts, "down");

  const discountUsersData = generateMiniChartData(discountUsers, "up");
  const wishlistUsersData = generateMiniChartData(wishlistUsers, "up");
  const reviewersData = generateMiniChartData(reviewers, "up");
  const emailSubscribersData = generateMiniChartData(emailSubscribers, "up");

  const morningPurchasesData = generateMiniChartData(morningPurchases, "up");
  const afternoonPurchasesData = generateMiniChartData(afternoonPurchases, "up");
  const eveningPurchasesData = generateMiniChartData(eveningPurchases, "up");
  const weekendPurchasesData = generateMiniChartData(weekendPurchases, "up");

  return {
    // Customer Overview
    totalCustomers,
    newCustomers,
    returningCustomers,
    inactiveCustomers,

    // Purchase & Order Behavior
    codOrders,
    prepaidOrders,
    cancelledOrders,
    abandonedCarts,

    // Engagement Patterns
    discountUsers,
    wishlistUsers,
    reviewers,
    emailSubscribers,

    // Purchase Timing
    morningPurchases,
    afternoonPurchases,
    eveningPurchases,
    weekendPurchases,

    // Chart data
    orderTypeData,
    engagementData,

    // Mini chart data
    customerGrowthData,
    newCustomersData,
    returningCustomersData,
    inactiveCustomersData,
    codOrdersData,
    prepaidOrdersData,
    cancelledOrdersData,
    abandonedCartsData,
    discountUsersData,
    wishlistUsersData,
    reviewersData,
    emailSubscribersData,
    morningPurchasesData,
    afternoonPurchasesData,
    eveningPurchasesData,
    weekendPurchasesData,

    // Polaris translations
    polarisTranslations
  };
};

/**
 * Main component for the dashboard
 */
export default function Dashboard() {
  const data = useLoaderData<typeof loader>();
  const [isLoading, setIsLoading] = useState(false);
  const [activeSegmentModal, setActiveSegmentModal] = useState<string | null>(null);
  const [dateRangeValue, setDateRangeValue] = useState('last30Days');

  // Function to handle the refresh button click
  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate a data refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1500);
  };

  // Function to handle the view segment button click
  const handleViewSegment = (segmentName: string) => {
    setActiveSegmentModal(segmentName);
  };

  // Function to get the appropriate chart data for each card
  const getChartDataForCard = (cardTitle: string, data: any) => {
    switch (cardTitle) {
      case "Total Customers":
        return data.customerGrowthData;
      case "New Customers":
        return data.newCustomersData;
      case "Returning Customers":
        return data.returningCustomersData;
      case "Inactive Customers":
        return data.inactiveCustomersData;
      case "COD Orders":
        return data.codOrdersData;
      case "Prepaid Orders":
        return data.prepaidOrdersData;
      case "Cancelled Orders":
        return data.cancelledOrdersData;
      case "Abandoned Carts":
        return data.abandonedCartsData;
      case "Discount Users":
        return data.discountUsersData;
      case "Wishlist Users":
        return data.wishlistUsersData;
      case "Reviewers":
        return data.reviewersData;
      case "Email Subscribers":
        return data.emailSubscribersData;
      case "Morning Purchases":
        return data.morningPurchasesData;
      case "Afternoon Purchases":
        return data.afternoonPurchasesData;
      case "Evening Purchases":
        return data.eveningPurchasesData;
      case "Weekend Purchases":
        return data.weekendPurchasesData;
      default:
        return data.customerGrowthData;
    }
  };

  // Function to get growth indicator text for each card
  const getGrowthIndicator = (cardTitle: string) => {
    switch (cardTitle) {
      case "Total Customers":
        return "↑ 25% growth in the last 6 months";
      case "New Customers":
        return "↑ 33% increase from January";
      case "Returning Customers":
        return "↑ 25% higher retention rate";
      case "Inactive Customers":
        return "↓ 9% decrease since January";
      case "COD Orders":
        return "↑ 25% increase in 6 months";
      case "Prepaid Orders":
        return "↑ 25% growth since January";
      case "Cancelled Orders":
        return "↓ 17% decrease in cancellations";
      case "Abandoned Carts":
        return "↓ 17% reduction in cart abandonment";
      case "Discount Users":
        return "↑ 25% more coupon usage";
      case "Wishlist Users":
        return "↑ 40% growth in wishlist usage";
      case "Reviewers":
        return "↑ 67% more customer reviews";
      case "Email Subscribers":
        return "↑ 18% growth in subscribers";
      case "Morning Purchases":
        return "↑ 25% increase in morning sales";
      case "Afternoon Purchases":
        return "↑ 25% growth in afternoon orders";
      case "Evening Purchases":
        return "↑ 25% more evening shoppers";
      case "Weekend Purchases":
        return "↑ 18% increase in weekend sales";
      default:
        return "↑ 25% growth in the last 6 months";
    }
  };

  // Function to get appropriate tone for growth indicator
  const getGrowthTone = (cardTitle: string) => {
    switch (cardTitle) {
      case "Inactive Customers":
      case "Cancelled Orders":
      case "Abandoned Carts":
        // For metrics where decrease is good
        return "success" as const;
      case "Discount Users":
        // For metrics where increase might need attention
        return "subdued" as const;
      default:
        // For metrics where increase is good
        return "success" as const;
    }
  };

  // Define the type for our insight cards
  interface InsightCard {
    title: string;
    value: number;
    status?: 'success' | 'info' | 'warning' | 'critical' | 'new';
    description?: string;
    showViewButton?: boolean;
  }

  // Define card sections
  const customerOverviewCards: InsightCard[] = [
    {
      title: "Total Customers",
      value: data.totalCustomers,
      status: "success",
      showViewButton: true,
    },
    {
      title: "New Customers",
      value: data.newCustomers,
      status: "new",
      description: "Joined in the last 30 days",
      showViewButton: true,
    },
    {
      title: "Returning Customers",
      value: data.returningCustomers,
      status: "success",
      description: "In the last 30 days",
      showViewButton: true,
    },
    {
      title: "Inactive Customers",
      value: data.inactiveCustomers,
      status: "warning",
      description: "No purchase in 90+ days",
      showViewButton: true,
    },
  ];

  const orderBehaviorCards: InsightCard[] = [
    {
      title: "COD Orders",
      value: data.codOrders,
      status: "info",
      showViewButton: true,
    },
    {
      title: "Prepaid Orders",
      value: data.prepaidOrders,
      status: "success",
      showViewButton: true,
    },
    {
      title: "Cancelled Orders",
      value: data.cancelledOrders,
      status: "critical",
      showViewButton: true,
    },
    {
      title: "Abandoned Carts",
      value: data.abandonedCarts,
      status: "warning",
      showViewButton: true,
    },
  ];

  const engagementCards: InsightCard[] = [
    {
      title: "Discount Users",
      value: data.discountUsers,
      status: "warning",
    },
    {
      title: "Wishlist Users",
      value: data.wishlistUsers,
      status: "info",
    },
    {
      title: "Reviewers",
      value: data.reviewers,
      status: "success",
    },
    {
      title: "Email Subscribers",
      value: data.emailSubscribers,
      status: "info",
    },
  ];

  const purchaseTimingCards: InsightCard[] = [
    {
      title: "Morning Purchases",
      value: data.morningPurchases,
      status: "info",
      description: "6 AM - 12 PM",
    },
    {
      title: "Afternoon Purchases",
      value: data.afternoonPurchases,
      status: "info",
      description: "12 PM - 6 PM",
    },
    {
      title: "Evening Purchases",
      value: data.eveningPurchases,
      status: "info",
      description: "6 PM - 12 AM",
    },
    {
      title: "Weekend Purchases",
      value: data.weekendPurchases,
      status: "success",
      description: "Saturday - Sunday",
    },
  ];

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleFont: {
          size: 14,
          weight: 'bold' as const
        },
        bodyFont: {
          size: 13
        },
        padding: 12,
        cornerRadius: 4,
        displayColors: true,
        boxWidth: 10,
        boxHeight: 10,
        usePointStyle: true
      }
    }
  };

  // Behavioral Breakdown chart options
  const behavioralChartOptions = {
    ...chartOptions,
    indexAxis: 'y' as const,
    plugins: {
      ...chartOptions.plugins,
      legend: {
        display: false
      },
      tooltip: {
        ...chartOptions.plugins.tooltip,
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.raw} users`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            size: 12
          }
        }
      },
      y: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 13,
            weight: 'bold' as const
          }
        }
      }
    },
    animation: {
      duration: 1000,
      easing: 'easeOutQuart' as const
    },
    barThickness: 25,
    borderRadius: 4
  };

  // Mini chart options
  const miniChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
    },
    scales: {
      x: {
        display: false,
      },
      y: {
        display: false,
      },
    },
    elements: {
      line: {
        tension: 0.4,
      },
      point: {
        radius: 0,
      },
    },
  };

  // Function to render a section with cards
  const renderSection = (title: string, cards: InsightCard[]) => (
    <Layout.Section>
      <BlockStack gap="400">
        <Text as="h2" variant="headingLg">
          {title}
        </Text>
        <Grid>
          {cards.map((card, index) => (
            <Grid.Cell key={index} columnSpan={{ xs: 6, sm: 6, md: 3, lg: 3, xl: 3 }}>
              <div style={{ height: '100%' }}>
                <Card padding="400">
                  <BlockStack gap="300">
                    {/* Card header with title and badge */}
                    <InlineStack align="space-between">
                      <Text as="h3" variant="headingMd">
                        {card.title}
                      </Text>
                      {card.status && (
                        <Badge tone={
                          card.status === 'new' ? 'info' :
                          card.status as 'success' | 'info' | 'warning' | 'critical'
                        }>
                          {card.status === 'new' ? 'New' :
                          card.status === 'success' ? 'Good' :
                          card.status === 'warning' ? 'Attention' :
                          card.status === 'critical' ? 'Issue' : 'Info'}
                        </Badge>
                      )}
                    </InlineStack>

                    {/* Value and menu icon */}
                    <InlineStack align="space-between" blockAlign="center">
                      <InlineStack gap="200" blockAlign="center">
                        <Text as="p" variant="heading2xl" fontWeight="bold">
                          {card.value.toLocaleString()}
                        </Text>

                        {/* Mini line charts for all cards */}
                        <div style={{ width: '60px', height: '30px' }}>
                          <Line
                            data={getChartDataForCard(card.title, data)}
                            options={miniChartOptions}
                          />
                        </div>
                      </InlineStack>

                      {card.showViewButton && (
                        <Button
                          icon={ClipboardIcon}
                          onClick={() => handleViewSegment(card.title)}
                          variant="primary"
                          accessibilityLabel={`View ${card.title} Segment`}
                        />
                      )}
                    </InlineStack>

                    {/* Description if available */}
                    {card.description && (
                      <Text as="p" variant="bodySm" tone="subdued">
                        {card.description}
                      </Text>
                    )}

                    {/* Growth indicator for all cards */}
                    {card.title === "Total Customers" ? (
                      <BlockStack gap="100">
                        <Text as="p" variant="bodySm" fontWeight="semibold">
                          Your store is doing good
                        </Text>
                        <Text as="p" variant="bodySm" tone={getGrowthTone(card.title)}>
                          {getGrowthIndicator(card.title)}
                        </Text>
                      </BlockStack>
                    ) : (
                      <Text as="p" variant="bodySm" tone={getGrowthTone(card.title)}>
                        {getGrowthIndicator(card.title)}
                      </Text>
                    )}
                  </BlockStack>
                </Card>
              </div>
            </Grid.Cell>
          ))}
        </Grid>
      </BlockStack>
    </Layout.Section>
  );

  return (
    <AppProvider i18n={data.polarisTranslations}>
      <Page fullWidth>
        <TitleBar title="Customer Insights Dashboard" />

        {/* Segment View Modal */}
        {activeSegmentModal && (
          <Modal
            open={!!activeSegmentModal}
            onClose={() => setActiveSegmentModal(null)}
            title={`${activeSegmentModal} Segment`}
            primaryAction={{
              content: 'Export Data',
              onAction: () => setActiveSegmentModal(null),
            }}
            secondaryActions={[
              {
                content: 'Close',
                onAction: () => setActiveSegmentModal(null),
              },
            ]}
          >
            <Modal.Section>
              <EmptyState
                heading={`${activeSegmentModal} Details`}
                image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
              >
                <p>
                  This is where you would see detailed information about the {activeSegmentModal.toLowerCase()} segment.
                  In a real application, this would include a table of data, filters, and additional metrics.
                </p>
              </EmptyState>
            </Modal.Section>
          </Modal>
        )}

        {/* Loading overlay */}
        {isLoading && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000,
          }}>
            <div style={{
              backgroundColor: 'white',
              padding: '20px',
              borderRadius: '8px',
              boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '12px'
            }}>
              <Spinner size="large" />
              <Text as="p" variant="bodyMd">
                Refreshing dashboard data...
              </Text>
            </div>
          </div>
        )}

        <BlockStack gap="600">
          <Card padding="400">
            <BlockStack gap="400">
              <InlineStack align="space-between" blockAlign="center">
                <InlineStack gap="400">
                  <Text variant="headingMd" as="h2">Dashboard Controls</Text>
                  <Select
                    label="Date range"
                    labelHidden
                    options={[
                      { label: 'Today', value: 'today' },
                      { label: 'Yesterday', value: 'yesterday' },
                      { label: 'Last 7 days', value: 'last7Days' },
                      { label: 'Last 30 days', value: 'last30Days' },
                      { label: 'Last 90 days', value: 'last90Days' },
                      { label: 'This month', value: 'thisMonth' },
                      { label: 'Last month', value: 'lastMonth' },
                      { label: 'Custom range', value: 'custom' },
                    ]}
                    value={dateRangeValue}
                    onChange={setDateRangeValue}
                  />
                </InlineStack>

                <Button
                  icon={isLoading ? undefined : RefreshIcon}
                  onClick={handleRefresh}
                  variant="primary"
                  loading={isLoading}
                  disabled={isLoading}
                >
                  {isLoading ? "Refreshing..." : "Refresh Data"}
                </Button>
              </InlineStack>

              <Text variant="bodyMd" as="p" tone="subdued">
                Currently showing data from {dateRangeValue === 'last30Days' ? 'the last 30 days' : 'the selected period'}.
                Last updated: {new Date().toLocaleString()}
              </Text>
            </BlockStack>
          </Card>

          <Layout>
            {/* Customer Overview Section */}
            {renderSection("Customers Overview", customerOverviewCards)}
          </Layout>

          <Layout>
            {/* Purchase & Order Behavior Section */}
            {renderSection("Purchase & Order Behavior", orderBehaviorCards)}
          </Layout>

          <Layout>
            {/* Engagement Patterns Section */}
            {renderSection("Engagement Patterns", engagementCards)}
          </Layout>

          {/* Charts Section - Side by Side */}
          <Layout>
            <Layout.Section>
              <Text as="h2" variant="headingLg">
                Visual Analytics
              </Text>
            </Layout.Section>
          </Layout>

          <Layout>
            {/* Customer Segmentation Chart */}
            <Layout.Section variant="oneHalf">
              <div style={{ height: '100%' }}>
                <Card padding="400">
                  <BlockStack gap="300">
                  <Text as="h3" variant="headingMd">
                    Customer Segmentation
                  </Text>
                  <div style={{ height: '350px', padding: '16px' }}>
                    <Pie data={data.orderTypeData} options={chartOptions} />
                  </div>
                  <Text as="p" variant="bodySm" tone="subdued">
                    Distribution of orders by payment type and status
                  </Text>
                  </BlockStack>
                </Card>
              </div>
            </Layout.Section>

            {/* Behavioral Breakdown Chart */}
            <Layout.Section variant="oneHalf">
              <div style={{ height: '100%' }}>
                <Card padding="400">
                  <BlockStack gap="300">
                  <Text as="h3" variant="headingMd">
                    Behavioral Breakdown
                  </Text>
                  <div style={{ height: '350px', padding: '16px' }}>
                    <Bar
                      data={data.engagementData}
                      options={behavioralChartOptions}
                    />
                  </div>
                  <InlineStack align="space-between">
                    <Text as="p" variant="bodySm" tone="subdued">
                      Comparison of different customer engagement types
                    </Text>
                    <Text as="p" variant="bodySm" tone="success">
                      ↑ 32% overall engagement growth
                    </Text>
                  </InlineStack>
                  </BlockStack>
                </Card>
              </div>
            </Layout.Section>
          </Layout>

          <Layout>
            {/* Purchase Timing Section */}
            {renderSection("Purchase Timing", purchaseTimingCards)}
          </Layout>
        </BlockStack>
      </Page>
    </AppProvider>
  );
}
